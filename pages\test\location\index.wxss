/* pages/test/location/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 40rpx 0;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.status {
  font-size: 28rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.status.active {
  color: #4CAF50;
  background-color: #E8F5E8;
}

.status.inactive {
  color: #FF5722;
  background-color: #FFEBEE;
}

.button-group {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  color: white;
}

.btn-primary {
  background-color: #2196F3;
}

.btn-success {
  background-color: #4CAF50;
}

.btn-danger {
  background-color: #FF5722;
}

.btn-small {
  flex: none;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  background-color: #999;
}

.btn[disabled] {
  background-color: #ccc !important;
  color: #999 !important;
}

.location-info {
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  background-color: #fafafa;
}

.no-data {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.test-note {
  margin-top: 15rpx;
  padding: 15rpx;
  background-color: #fff3cd;
  border-radius: 8rpx;
  border-left: 4rpx solid #ffc107;
}

.test-note text {
  font-size: 24rpx;
  color: #856404;
}

.log-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.log-item {
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #ddd;
}

.log-item.success {
  background-color: #E8F5E8;
  border-left-color: #4CAF50;
}

.log-item.error {
  background-color: #FFEBEE;
  border-left-color: #FF5722;
}

.log-time {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.log-message {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.log-data {
  font-size: 24rpx;
  color: #666;
}

.log-data text {
  display: block;
  margin-bottom: 4rpx;
}

.detail-status {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}
