# 员工端位置服务功能说明

## 功能概述

员工端位置服务是一个后台定时静默上报位置的功能，用于实时跟踪员工位置并自动更新关联车辆的位置信息。

## 主要特性

1. **自动启动**: 用户登录后自动启动位置服务
2. **后台运行**: 支持小程序后台运行时继续上报位置
3. **定时上报**: 每30秒自动上报一次位置信息
4. **权限管理**: 自动处理位置权限申请
5. **错误重试**: 上报失败时自动重试机制
6. **降级处理**: 后台位置权限不可用时降级到前台位置

## 技术实现

### 1. API接口

**接口地址**: `POST /openapi/location/updateEmployeeLocation`

**请求参数**:
```json
{
  "employeeId": 1,
  "latitude": 39.916527,
  "longitude": 116.397128,
  "address": "北京市朝阳区某某街道"
}
```

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "employeeId": 1,
    "vehicleId": 5,
    "plateNumber": "京A12345",
    "latitude": 39.916527,
    "longitude": 116.397128,
    "address": "北京市朝阳区某某街道",
    "updateTime": "2024-01-15T10:30:00.000Z",
    "message": "位置更新成功"
  }
}
```

### 2. 核心模块

#### LocationManager (位置管理器)
- 文件位置: `utils/LocationManager.js`
- 功能: 管理位置服务的启动、停止、定时上报等
- 特性: 单例模式，全局唯一实例

#### Location API模块
- 文件位置: `api/modules/location.js`
- 功能: 封装位置上报API接口

### 3. 权限要求

小程序需要以下权限：
- `scope.userLocation`: 获取当前位置
- `scope.userLocationBackground`: 后台获取位置（可选）

### 4. 生命周期管理

#### 启动时机
- 小程序启动时检查用户登录状态
- 用户登录成功后自动启动
- 小程序从后台切换到前台时检查并启动

#### 停止时机
- 用户登出时自动停止
- 登录过期时自动停止
- 手动调用停止方法

## 使用方法

### 1. 自动使用（推荐）

位置服务会在用户登录后自动启动，无需手动干预。

### 2. 手动控制

```javascript
import LocationManager from '../utils/LocationManager';

// 启动位置服务
LocationManager.start();

// 停止位置服务
LocationManager.stop();

// 检查运行状态
const isRunning = LocationManager.isLocationServiceRunning();
```

### 3. 状态监控

在"我的"页面可以查看位置服务的运行状态，点击可以手动切换。

### 4. 测试功能

访问"我的" -> "位置测试"页面可以进行功能测试：
- 查看位置服务状态
- 手动获取位置
- 手动上报位置
- 自动测试功能
- 查看上报日志

## 配置参数

### LocationManager配置

```javascript
this.uploadInterval = 30000; // 上报间隔（30秒）
this.maxRetryCount = 3; // 最大重试次数
```

### 腾讯地图API密钥

地址解析使用腾讯地图API，密钥配置在：
- `utils/LocationManager.js`
- `pages/mine/checkin/index.js`
- `pages/orders/orderDetail/index.js`
- `pages/orders/index.js`
- `pages/index/index.js`

## 注意事项

1. **权限申请**: 首次使用需要用户授权位置权限
2. **后台限制**: 微信小程序对后台位置有严格限制，需要特殊申请
3. **电量消耗**: 持续获取位置会增加电量消耗
4. **网络依赖**: 位置上报需要网络连接
5. **精度影响**: GPS信号弱的地方可能影响位置精度

## 故障排除

### 1. 位置服务无法启动
- 检查用户是否已登录
- 检查位置权限是否已授权
- 查看控制台错误日志

### 2. 位置上报失败
- 检查网络连接
- 检查API接口是否正常
- 查看上报日志中的错误信息

### 3. 后台位置不工作
- 检查是否有后台位置权限
- 确认小程序是否支持后台位置
- 查看是否降级到前台位置模式

## 开发调试

### 1. 日志输出
所有关键操作都有控制台日志输出，便于调试。

### 2. 测试页面
使用测试页面可以方便地测试各项功能。

### 3. 状态显示
"我的"页面显示位置服务状态，便于监控。

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持自动位置上报
- 支持权限管理
- 支持错误重试
- 添加测试功能
