import request, { analysisRes } from "../request";
import config from "../config";

const { location } = config.apiUrls;

export default {
  /**
   * 员工端上报当前位置
   * @param {number} employeeId 员工ID
   * @param {number} latitude 纬度（-90到90之间）
   * @param {number} longitude 经度（-180到180之间）
   * @param {string} address 当前地址描述（可选）
   */
  async updateEmployeeLocation(employeeId, latitude, longitude, address = '') {
    try {
      const res = await request.post(location.updateEmployeeLocation, {
        employeeId,
        latitude,
        longitude,
        address,
      });
      const data = analysisRes(res);

      // 对于业务错误（如员工未绑定车辆），也返回成功，避免影响位置服务运行
      if (data || (res && res.errCode === 400)) {
        console.log('LocationManager API: 位置上报请求已发送', res);
        return { success: true, message: '位置信息已上报' };
      }

      return data;
    } catch (error) {
      // 静默处理网络错误
      console.log('LocationManager API: 位置上报网络错误', error.message || error);
      return null;
    }
  },
};
