<!--pages/test/location/index.wxml-->
<view class="container">
  <view class="header">
    <text class="title">位置服务测试</text>
  </view>

  <!-- 用户信息 -->
  <view class="section">
    <view class="section-title">用户信息</view>
    <view class="info-item">
      <text class="label">用户ID:</text>
      <text class="value">{{userInfo.id || '未登录'}}</text>
    </view>
    <view class="info-item">
      <text class="label">用户名:</text>
      <text class="value">{{userInfo.name || '未登录'}}</text>
    </view>
  </view>

  <!-- 位置服务状态 -->
  <view class="section">
    <view class="section-title">位置服务状态</view>
    <view class="status-item">
      <text class="label">服务状态:</text>
      <text class="status {{locationServiceStatus ? 'active' : 'inactive'}}">
        {{locationServiceStatus ? '运行中' : '已停止'}}
      </text>
    </view>
    <view class="button-group">
      <button class="btn btn-primary" bind:tap="startLocationService" disabled="{{locationServiceStatus}}">
        启动服务
      </button>
      <button class="btn btn-danger" bind:tap="stopLocationService" disabled="{{!locationServiceStatus}}">
        停止服务
      </button>
    </view>
  </view>

  <!-- 位置信息 -->
  <view class="section">
    <view class="section-title">当前位置</view>
    <view wx:if="{{lastLocation}}" class="location-info">
      <view class="info-item">
        <text class="label">纬度:</text>
        <text class="value">{{lastLocation.latitude}}</text>
      </view>
      <view class="info-item">
        <text class="label">经度:</text>
        <text class="value">{{lastLocation.longitude}}</text>
      </view>
      <view class="info-item">
        <text class="label">精度:</text>
        <text class="value">{{lastLocation.accuracy}}米</text>
      </view>
      <view class="info-item">
        <text class="label">时间:</text>
        <text class="value">{{lastLocation.timestamp}}</text>
      </view>
    </view>
    <view wx:else class="no-data">
      <text>暂无位置信息</text>
    </view>
    <view class="button-group">
      <button class="btn btn-primary" bind:tap="getCurrentLocation">获取位置</button>
      <button class="btn btn-success" bind:tap="manualUploadLocation" disabled="{{!lastLocation}}">
        上报位置
      </button>
    </view>
  </view>

  <!-- 自动测试 -->
  <view class="section">
    <view class="section-title">自动测试</view>
    <view class="info-item">
      <text class="label">测试状态:</text>
      <text class="status {{isManualTesting ? 'active' : 'inactive'}}">
        {{isManualTesting ? '测试中' : '已停止'}}
      </text>
    </view>
    <view class="button-group">
      <button class="btn btn-primary" bind:tap="startManualTest" disabled="{{isManualTesting}}">
        开始测试
      </button>
      <button class="btn btn-danger" bind:tap="stopManualTest" disabled="{{!isManualTesting}}">
        停止测试
      </button>
    </view>
    <view class="test-note">
      <text>自动测试将每10秒获取并上报一次位置</text>
    </view>
  </view>

  <!-- 上报日志 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">上报日志</text>
      <button class="btn btn-small" bind:tap="clearLogs" disabled="{{uploadLogs.length === 0}}">
        清除日志
      </button>
    </view>
    <view wx:if="{{uploadLogs.length > 0}}" class="log-list">
      <view class="log-item {{item.status}}" wx:for="{{uploadLogs}}" wx:key="index">
        <view class="log-time">{{item.time}}</view>
        <view class="log-message">{{item.message}}</view>
        <view wx:if="{{item.data}}" class="log-data">
          <text>车辆ID: {{item.data.vehicleId || '无'}}</text>
          <text>车牌号: {{item.data.plateNumber || '无'}}</text>
        </view>
      </view>
    </view>
    <view wx:else class="no-data">
      <text>暂无上报日志</text>
    </view>
  </view>
</view>
