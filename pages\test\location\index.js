// pages/test/location/index.js
import LocationManager from '../../../utils/LocationManager';
import locationApi from '../../../api/modules/location';
import Session from '../../../common/Session';

Page({
  data: {
    userInfo: null,
    locationServiceStatus: false,
    lastLocation: null,
    uploadLogs: [],
    isManualTesting: false,
  },

  onLoad() {
    this.initUserInfo();
    this.updateLocationServiceStatus();
  },

  onShow() {
    this.updateLocationServiceStatus();
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再测试位置服务',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/login/index',
            });
          }
        }
      });
      return;
    }
    this.setData({ userInfo });
  },

  // 更新位置服务状态
  updateLocationServiceStatus() {
    const isRunning = LocationManager.isLocationServiceRunning();
    this.setData({
      locationServiceStatus: isRunning,
    });
  },

  // 启动位置服务
  startLocationService() {
    LocationManager.start();
    console.log('测试页面: 启动位置服务');

    setTimeout(() => {
      this.updateLocationServiceStatus();
    }, 1000);
  },

  // 停止位置服务
  stopLocationService() {
    LocationManager.stop();
    this.updateLocationServiceStatus();
    console.log('测试页面: 停止位置服务');
  },

  // 手动获取当前位置
  async getCurrentLocation() {
    wx.showLoading({
      title: '获取位置中...',
    });

    try {
      const location = await this.getLocationData();
      if (location) {
        this.setData({
          lastLocation: location,
        });
        wx.showToast({
          title: '位置获取成功',
          icon: 'success',
        });
      }
    } catch (error) {
      console.error('获取位置失败:', error);
      wx.showToast({
        title: '位置获取失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 获取位置数据
  getLocationData() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            timestamp: new Date().toLocaleString(),
          };
          resolve(location);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  // 手动上报位置
  async manualUploadLocation() {
    if (!this.data.userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      return;
    }

    if (!this.data.lastLocation) {
      wx.showToast({
        title: '请先获取位置',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '上报位置中...',
    });

    try {
      const { latitude, longitude } = this.data.lastLocation;
      const address = await this.getAddressFromLocation(latitude, longitude);
      
      const result = await locationApi.updateEmployeeLocation(
        this.data.userInfo.id,
        latitude,
        longitude,
        address
      );

      if (result) {
        const log = {
          time: new Date().toLocaleString(),
          status: 'success',
          message: '位置上报成功',
          data: result,
        };
        
        this.setData({
          uploadLogs: [log, ...this.data.uploadLogs.slice(0, 9)], // 保留最近10条记录
        });

        wx.showToast({
          title: '位置上报成功',
          icon: 'success',
        });
      } else {
        throw new Error('上报失败');
      }
    } catch (error) {
      console.error('位置上报失败:', error);
      
      const log = {
        time: new Date().toLocaleString(),
        status: 'error',
        message: '位置上报失败: ' + error.message,
        data: null,
      };
      
      this.setData({
        uploadLogs: [log, ...this.data.uploadLogs.slice(0, 9)],
      });

      wx.showToast({
        title: '位置上报失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 根据经纬度获取地址信息
  getAddressFromLocation(latitude, longitude) {
    return new Promise((resolve) => {
      wx.request({
        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
        data: {
          location: `${latitude},${longitude}`,
          key: 'OQRBZ-KZXKF-XEEJJ-YNVQQ-QZJQS-XQFQH',
          get_poi: 1
        },
        success: (res) => {
          let address = '';
          if (res.data && res.data.status === 0 && res.data.result) {
            const result = res.data.result;
            address = result.formatted_addresses?.recommend ||
                     result.address ||
                     result.formatted_addresses?.rough ||
                     '';
          }
          resolve(address);
        },
        fail: (error) => {
          console.error('地址解析失败:', error);
          resolve('');
        }
      });
    });
  },

  // 清除上报日志
  clearLogs() {
    this.setData({
      uploadLogs: [],
    });
    wx.showToast({
      title: '日志已清除',
      icon: 'success',
    });
  },

  // 开始手动测试
  startManualTest() {
    this.setData({
      isManualTesting: true,
    });
    
    // 每10秒自动获取并上报位置
    this.testInterval = setInterval(() => {
      this.autoTestUpload();
    }, 10000);

    wx.showToast({
      title: '开始自动测试',
      icon: 'success',
    });
  },

  // 停止手动测试
  stopManualTest() {
    this.setData({
      isManualTesting: false,
    });
    
    if (this.testInterval) {
      clearInterval(this.testInterval);
      this.testInterval = null;
    }

    wx.showToast({
      title: '停止自动测试',
      icon: 'success',
    });
  },

  // 自动测试上报
  async autoTestUpload() {
    try {
      await this.getCurrentLocation();
      await this.manualUploadLocation();
    } catch (error) {
      console.error('自动测试失败:', error);
    }
  },

  onUnload() {
    // 页面卸载时清除测试定时器
    if (this.testInterval) {
      clearInterval(this.testInterval);
      this.testInterval = null;
    }
  },
});
