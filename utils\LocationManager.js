import locationApi from '../api/modules/location';
import Session from '../common/Session';

/**
 * 位置管理器 - 处理员工端后台位置上报
 */
class LocationManager {
  constructor() {
    this.isRunning = false; // 是否正在运行
    this.uploadTimer = null; // 上报定时器
    this.lastLocation = null; // 最后一次位置信息
    this.uploadInterval = 30000; // 上报间隔（30秒）
    this.maxRetryCount = 3; // 最大重试次数
    this.retryCount = 0; // 当前重试次数
    this.isLocationChangeListening = false; // 是否正在监听位置变化
  }

  /**
   * 启动位置服务
   */
  async start() {
    console.log('LocationManager: 启动位置服务');
    
    if (this.isRunning) {
      console.log('LocationManager: 位置服务已在运行');
      return;
    }

    // 检查用户登录状态
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      console.log('LocationManager: 用户未登录，无法启动位置服务');
      return;
    }

    try {
      // 检查并请求位置权限
      const hasPermission = await this.checkLocationPermission();
      if (!hasPermission) {
        console.log('LocationManager: 位置权限未授权');
        return;
      }

      // 启动后台位置更新
      await this.startBackgroundLocationUpdate();
      
      // 开始定时上报
      this.startLocationUpload();
      
      this.isRunning = true;
      console.log('LocationManager: 位置服务启动成功');
    } catch (error) {
      console.error('LocationManager: 启动位置服务失败', error);
    }
  }

  /**
   * 停止位置服务
   */
  stop() {
    console.log('LocationManager: 停止位置服务');
    
    this.isRunning = false;
    
    // 清除定时器
    if (this.uploadTimer) {
      clearInterval(this.uploadTimer);
      this.uploadTimer = null;
    }

    // 停止位置更新（同时尝试停止前台和后台）
    try {
      if (typeof wx.stopLocationUpdate === 'function') {
        wx.stopLocationUpdate();
        console.log('LocationManager: 已停止位置更新');
      }
    } catch (error) {
      console.error('LocationManager: 停止位置更新失败', error);
    }

    // 移除位置变化监听
    if (this.isLocationChangeListening) {
      try {
        if (typeof wx.offLocationChange === 'function') {
          wx.offLocationChange();
        }
        this.isLocationChangeListening = false;
        console.log('LocationManager: 已移除位置变化监听');
      } catch (error) {
        console.error('LocationManager: 移除位置变化监听失败', error);
      }
    }
  }

  /**
   * 检查位置权限
   */
  checkLocationPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            // 已授权，检查后台位置权限
            if (res.authSetting['scope.userLocationBackground']) {
              resolve(true);
            } else {
              // 静默请求后台位置权限，失败也不影响
              wx.authorize({
                scope: 'scope.userLocationBackground',
                success: () => {
                  console.log('LocationManager: 后台位置权限获取成功');
                  resolve(true);
                },
                fail: () => {
                  console.log('LocationManager: 后台位置权限未获取，使用前台位置');
                  // 即使没有后台权限，也可以使用前台位置
                  resolve(true);
                }
              });
            }
          } else {
            // 静默请求位置权限
            wx.authorize({
              scope: 'scope.userLocation',
              success: () => {
                console.log('LocationManager: 位置权限获取成功');
                // 位置权限获取成功，再静默请求后台位置权限
                wx.authorize({
                  scope: 'scope.userLocationBackground',
                  success: () => {
                    console.log('LocationManager: 后台位置权限获取成功');
                    resolve(true);
                  },
                  fail: () => {
                    console.log('LocationManager: 后台位置权限未获取，使用前台位置');
                    resolve(true);
                  }
                });
              },
              fail: () => {
                console.log('LocationManager: 位置权限未获取，位置服务无法启动');
                resolve(false);
              }
            });
          }
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }

  /**
   * 启动后台位置更新
   */
  startBackgroundLocationUpdate() {
    return new Promise((resolve, reject) => {
      // 检查是否支持后台位置更新
      if (typeof wx.startLocationUpdateBackground === 'function') {
        // 尝试启动后台位置更新
        wx.startLocationUpdateBackground({
          type: 'gcj02',
          success: () => {
            console.log('LocationManager: 后台位置更新启动成功');

            // 监听位置变化
            if (!this.isLocationChangeListening && typeof wx.onLocationChange === 'function') {
              wx.onLocationChange((res) => {
                this.handleLocationChange(res);
              });
              this.isLocationChangeListening = true;
            }

            resolve();
          },
          fail: (error) => {
            console.error('LocationManager: 启动后台位置更新失败，尝试前台位置更新', error);
            this.startForegroundLocationUpdate(resolve, reject);
          }
        });
      } else {
        console.log('LocationManager: 不支持后台位置更新，使用前台位置更新');
        this.startForegroundLocationUpdate(resolve, reject);
      }
    });
  }

  /**
   * 启动前台位置更新
   */
  startForegroundLocationUpdate(resolve, reject) {
    if (typeof wx.startLocationUpdate === 'function') {
      wx.startLocationUpdate({
        type: 'gcj02',
        success: () => {
          console.log('LocationManager: 前台位置更新启动成功');

          // 监听位置变化
          if (!this.isLocationChangeListening && typeof wx.onLocationChange === 'function') {
            wx.onLocationChange((res) => {
              this.handleLocationChange(res);
            });
            this.isLocationChangeListening = true;
          }

          resolve();
        },
        fail: (error) => {
          console.error('LocationManager: 前台位置更新失败', error);
          reject(error);
        }
      });
    } else {
      console.log('LocationManager: 不支持位置更新API，仅使用定时获取位置');
      resolve();
    }
  }

  /**
   * 处理位置变化
   */
  handleLocationChange(location) {
    console.log('LocationManager: 位置发生变化', location);
    this.lastLocation = {
      latitude: location.latitude,
      longitude: location.longitude,
      accuracy: location.accuracy,
      timestamp: Date.now()
    };
  }

  /**
   * 开始定时上报位置
   */
  startLocationUpload() {
    // 立即上报一次
    this.uploadLocation();

    // 设置定时上报
    this.uploadTimer = setInterval(() => {
      this.uploadLocation();
    }, this.uploadInterval);

    console.log(`LocationManager: 开始定时上报位置，间隔${this.uploadInterval / 1000}秒`);
  }

  /**
   * 上报位置到服务器
   */
  async uploadLocation() {
    try {
      const userInfo = Session.getUser();
      if (!userInfo || !userInfo.id) {
        console.log('LocationManager: 用户未登录，跳过位置上报');
        return;
      }

      let location = this.lastLocation;
      
      // 如果没有缓存的位置信息，尝试获取当前位置
      if (!location) {
        location = await this.getCurrentLocation();
      }

      if (!location) {
        console.log('LocationManager: 无法获取位置信息，跳过上报');
        return;
      }

      // 获取地址信息（可选）
      const address = await this.getAddressFromLocation(location.latitude, location.longitude);

      // 上报位置
      const result = await locationApi.updateEmployeeLocation(
        userInfo.id,
        location.latitude,
        location.longitude,
        address
      );

      if (result) {
        console.log('LocationManager: 位置上报成功', result);
        this.retryCount = 0; // 重置重试次数
      } else {
        // 静默处理失败，不抛出错误避免影响用户体验
        console.log('LocationManager: 位置上报失败，但继续运行');
        this.handleUploadError();
      }
    } catch (error) {
      // 静默处理错误，只记录日志，不影响用户体验
      console.log('LocationManager: 位置上报遇到错误，继续运行', error.message || error);
      this.handleUploadError();
    }
  }

  /**
   * 获取当前位置
   */
  getCurrentLocation() {
    return new Promise((resolve) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            timestamp: Date.now()
          };
          this.lastLocation = location;
          resolve(location);
        },
        fail: (error) => {
          console.error('LocationManager: 获取当前位置失败', error);
          resolve(null);
        }
      });
    });
  }

  /**
   * 根据经纬度获取地址信息
   */
  getAddressFromLocation(latitude, longitude) {
    return new Promise((resolve) => {
      wx.request({
        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
        data: {
          location: `${latitude},${longitude}`,
          key: 'OQRBZ-KZXKF-XEEJJ-YNVQQ-QZJQS-XQFQH', // 腾讯地图API密钥
          get_poi: 1
        },
        success: (res) => {
          let address = '';
          if (res.data && res.data.status === 0 && res.data.result) {
            const result = res.data.result;
            address = result.formatted_addresses?.recommend ||
                     result.address ||
                     result.formatted_addresses?.rough ||
                     '';
          }
          resolve(address);
        },
        fail: (error) => {
          console.error('LocationManager: 地址解析失败', error);
          resolve('');
        }
      });
    });
  }

  /**
   * 处理上报错误
   */
  handleUploadError() {
    this.retryCount++;

    if (this.retryCount >= this.maxRetryCount) {
      console.log('LocationManager: 达到最大重试次数，暂停位置上报');
      // 暂停一段时间后重新开始，但不停止位置服务
      setTimeout(() => {
        this.retryCount = 0;
        console.log('LocationManager: 重置重试次数，继续位置上报');
      }, 300000); // 5分钟后重置，给服务器更多时间
    }
  }

  /**
   * 获取运行状态
   */
  isLocationServiceRunning() {
    return this.isRunning;
  }
}

// 创建单例实例
const locationManager = new LocationManager();

export default locationManager;
